{
    "type": "POST",
    "url": "{HOST}/api/v1.0/users/mockup/actions/add",
    "title": "Add user mockup data",
    "description": "\u003Cp\u003EAPI thêm dữ liệu mockup cho nhân viên\u003C/p\u003E",
    "group": "Mockup",
    "version": "1.0.0",
    "name": "AddUserMockupData",
    "parameter": {
      "fields": {
        "Body:": [
          {
            "group": "Body:",
            "type": "Array",
            "optional": false,
            "field": "data",
            "description": "\u003Cp\u003EDanh sách nhân viên. \u003Ccode\u003ETối đa 200 phần tử\u003C/code\u003E\u003C/p\u003E"
          },
          {
            "group": "Body:",
            "type": "String",
            "optional": false,
            "field": "data.employee_code",
            "description": "\u003Cp\u003EMã nhân viên.\u003C/p\u003E"
          },
          {
            "group": "Body:",
            "type": "String",
            "optional": false,
            "field": "data.name",
            "description": "\u003Cp\u003ETên nhân viên.\u003C/p\u003E"
          },
          {
            "group": "Body:",
            "type": "String",
            "optional": false,
            "field": "data.email_work",
            "description": "\u003Cp\u003EEmail làm việc của nhân viên.\u003C/p\u003E"
          },
          {
            "group": "Body:",
            "type": "String",
            "optional": true,
            "field": "data.personal_email",
            "description": "\u003Cp\u003EEmail cá nhân của nhân viên.\u003C/p\u003E"
          },
          {
            "group": "Body:",
            "type": "String",
            "optional": false,
            "field": "data.gender",
            "description": "\u003Cp\u003EGiới tính \u003Cul\u003E\u003Cbr\u003E \u003Cli\u003E1: Nam\u003C/li\u003E \u003Cli\u003E2: Nữ\u003C/li\u003E \u003C/ul\u003E\u003C/p\u003E"
          },
          {
            "group": "Body:",
            "type": "String",
            "optional": false,
            "field": "data.date_of_birth",
            "description": "\u003Cp\u003ENgày sinh của nhân viên. \u003Ccode\u003EFormat: YYYY-MM-DD\u003C/code\u003E\u003C/p\u003E"
          },
          {
            "group": "Body:",
            "type": "String",
            "optional": false,
            "field": "data.phone_number",
            "description": "\u003Cp\u003ESố điện thoại của nhân viên.\u003C/p\u003E"
          },
          {
            "group": "Body:",
            "type": "String",
            "optional": true,
            "field": "data.leader_email",
            "description": "\u003Cp\u003EEmail leader của nhân viên.\u003C/p\u003E"
          },
          {
            "group": "Body:",
            "type": "String",
            "optional": false,
            "field": "data.department_name",
            "description": "\u003Cp\u003ETên phòng ban của nhân viên. \u003Ccode\u003ETruyền BOM / BOD để set làm C Level\u003C/code\u003E\u003C/p\u003E"
          },
          {
            "group": "Body:",
            "type": "String",
            "optional": false,
            "field": "data.job_title_name",
            "description": "\u003Cp\u003ETên chức danh của nhân viên.\u003C/p\u003E"
          },
          {
            "group": "Body:",
            "type": "String",
            "optional": false,
            "field": "data.employee_type",
            "description": "\u003Cp\u003ELoại nhân viên \u003Cul\u003E\u003Cbr\u003E \u003Cli\u003E1: Chính thức\u003C/li\u003E \u003Cli\u003E2: Thử việc\u003C/li\u003E \u003C/ul\u003E\u003C/p\u003E"
          },
          {
            "group": "Body:",
            "type": "String",
            "optional": false,
            "field": "data.start_onboard_at",
            "description": "\u003Cp\u003ENgày làm việc của nhân viên. \u003Ccode\u003EFormat: YYYY-MM-DD\u003C/code\u003E\u003C/p\u003E"
          },
          {
            "group": "Body:",
            "type": "String",
            "optional": false,
            "field": "data.avatar_link",
            "description": "\u003Cp\u003ELink avatar của nhân viên.\u003C/p\u003E"
          },
          {
            "group": "Body:",
            "type": "String",
            "optional": false,
            "field": "data.thumb_avatar_link",
            "description": "\u003Cp\u003ELink avatar nhỏ của nhân viên.\u003C/p\u003E"
          },
          {
            "group": "Body:",
            "type": "Boolean",
            "optional": false,
            "field": "data.is_leader",
            "description": "\u003Cp\u003ECó phải leader hay không.\u003C/p\u003E"
          }
        ],
        "Query:": [
          {
            "group": "Query:",
            "type": "String",
            "allowedValues": [
              "vi",
              "en"
            ],
            "optional": true,
            "field": "lang",
            "defaultValue": "vi",
            "description": "\u003Cp\u003EMã ngôn ngữ. Nếu client không request lên mặc định là tiếng Việt.\u003C/p\u003E"
          }
        ]
      },
      "examples": [
        {
          "title": "Body example",
          "content": "{\n    \"data\": [\n        {\n            \"employee_code\": \"1234567890\",\n            \"name\": \"Nguyễn Văn A\",\n            \"email_work\": \"<EMAIL>\",\n            \"personal_email\": \"<EMAIL>\",\n            \"gender\": 1,\n            \"date_of_birth\": \"2000-01-01\",\n            \"phone_number\": \"0909090909\",\n            \"leader_email\": \"<EMAIL>\",\n            \"department_name\": \"BOM\",\n            \"job_title_name\": \"Lập trình viên\",\n            \"employee_type\": 1,\n            \"start_onboard_at\": \"2024-01-01\",\n            \"avatar_link\": \"https://example.com/avatar.png\",\n            \"thumb_avatar_link\": \"https://example.com/thumb_avatar.png\",\n            \"is_leader\": true\n        },\n        {\n            \"employee_code\": \"1234567891\",\n            \"name\": \"Nguyễn Văn B   \",\n            \"email_work\": \"<EMAIL>\",\n            \"personal_email\": \"<EMAIL>\",\n            \"gender\": 2,\n            \"date_of_birth\": \"2000-01-01\",\n            \"phone_number\": \"0909090909\",\n            \"leader_email\": \"<EMAIL>\",\n            \"department_name\": \"BOD\",\n            \"job_title_name\": \"Lập trình viên\",\n            \"employee_type\": 1,\n            \"start_onboard_at\": \"2024-01-01\",\n            \"avatar_link\": \"https://example.com/avatar.png\",\n            \"thumb_avatar_link\": \"https://example.com/thumb_avatar.png\",\n            \"is_leader\": false\n        }\n    ]\n}",
          "type": "json"
        }
      ]
    },
    "success": {
      "fields": {
        "Success 200": [
          {
            "group": "Success 200",
            "type": "String",
            "optional": false,
            "field": "message",
            "description": "\u003Cp\u003EMô tả phản hồi.\u003C/p\u003E"
          },
          {
            "group": "Success 200",
            "type": "Integer",
            "optional": false,
            "field": "code",
            "description": "\u003Cp\u003EMã phản hồi.\u003C/p\u003E"
          }
        ]
      },
      "examples": [
        {
          "title": "Response: HTTP/1.1 200 OK",
          "content": "{\n    \"code\": \"200\",\n    \"message\": \"Add success\"\n}",
          "type": "json"
        }
      ]
    },
    "filename": "src/ladder/mockup.py",
    "groupTitle": "Mockup",
    "header": {
      "fields": {
        "Headers:": [
          {
            "group": "Headers:",
            "type": "String",
            "optional": false,
            "field": "Authorization",
            "description": "\u003Cp\u003EToken/api-key để sử dụng api. Nếu typically là:\u003C/p\u003E \u003Cli\u003E\u003Ccode\u003EBearer\u003C/code\u003E thì Authenticate là AuthToken\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003EBasic\u003C/code\u003E thì Authenticate là API-Key\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003EDigest\u003C/code\u003E Sử dụng với Mobile API\u003C/li\u003E"
          }
        ]
      },
      "examples": [
        {
          "title": "AuthToken Example:",
          "content": "{\n    \"Authorization\":\"Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBbmhEYWlEaWVuIjoiaHR0cDovL3N0b3JhZ2UuZ29vZ2xlYXBpcy5jb20vdGVzdF94bG95YWx0eTEvaW1hZ2VzL25oYWN1bmdjYXAvMWI5OWJkY2YtZDU4Mi00ZjQ5LTk3MTUtMWI2MWRmZmYzOTI0PzE0NzY3ODEyODY3MDciLCJOaGFDdW5nQ2FwSUQiOiIxYjk5YmRjZi1kNTgyLTRmNDktOTcxNS0xYjYxZGZmZjM5MjQiLCJUcmFuZ1RoYWlUaGFtR2lheFBvaW50IjozLCJNYU5oYUN1bmdDYXAiOiJQSU5HQ09NU0hPUCIsIlF1eWVuIjpbIlJPTEVfQ0FVSElOSERBTkhNVUNUSUNIRElFTU1VQUhBTkdPRkZMSU5FIiwiUk9MRV9DQVVISU5ITkhBTlRIT05HQkFPIiwiUk9MRV9DQVVISU5ITkhBTlRIT05HQkFPQ09OR05PIiwiUk9MRV9DQVVISU5IVEFOR0RJRU0iLCJST0xFX0NPTU1FTlQiLCJST0xFX0NVQUhBTkciLCJST0xFX0RBTkdOSEFQIiwiUk9MRV9EQVNIQk9BUkRfRElFTSIsIlJPTEVfREFTSEJPQVJEX0RPSVFVQSIsIlJPTEVfREFTSEJPQVJEX0ZPT1RUUkFGRklDIiwiUk9MRV9EQVNIQk9BUkRfUkFUSU5HQ1VBSEFORyIsIlJPTEVfREFTSEJPQVJEX1ZPVUNIRVIiLCJST0xFX0VYQ0VMVEFOR0RJRU0iLCJST0xFX0ZPT1RUUkFGRklDIiwiUk9MRV9LSEFDSEhBTkdEQU5HS1kiLCJST0xFX0tIVVlFTk1BSSIsIlJPTEVfTElDSFNVRE9JUVVBIiwiUk9MRV9MSUNIU1VLSEFDSEhBTkdEVU5HVk9VQ0hFUiIsIlJPTEVfTElDSFNVVEFOR0RJRU0iLCJST0xFX0xZRE9LSEFDSEhBTkdSQVRJTkdUSEFQIiwiUk9MRV9OSEFDVU5HQ0FQIiwiUk9MRV9RVUFOTFlDT05HTk9DSElUSUVUIiwiUk9MRV9RVUFOTFlUSEUiLCJST0xFX1FVQU5UUklCRUFDT04iLCJST0xFX1JBVElOR0NVQUhBTkciLCJST0xFX1JFUE9SVF9ET0lRVUEiLCJST0xFX1JFUE9SVF9QVVJDSEFTRUZPT1RUUkFGRklDIiwiUk9MRV9SRVBPUlRfVk9VQ0hFUiIsIlJPTEVfU0FOUEhBTSIsIlJPTEVfVEFPVEFJS0hPQU4iLCJST0xFX1RBT1RIRVRVRklMRSIsIlJPTEVfVk9VQ0hFUiJdLCJUZW5OaGFDdW5nQ2FwIjoiUElOR0NPTSBTSE9QIiwiaWF0IjoxNTAyMTU3NTAyLCJUZW5UcnV5Q2FwIjoicGluZ2NvbXNob3BAcGluZ2NvbXNob3AiLCJUYWlLaG9hblF1YW5UcmlJRCI6IjFhNzE0YTgzLWIxY2UtNDE4MS1hMDk4LTA0MjYyMDdlMWYzNCJ9.PmnQpD617Z9mkw5WubKemSJvYkZYiFz6E1NigL7IHYQ\"\n}",
          "type": "json"
        },
        {
          "title": "Basic Token Example:",
          "content": "{\n    \"Authorization\":\"Basic 25630732-2976-444a-ba7a-3ca1b48fcc3a\"\n}",
          "type": "json"
        },
        {
          "title": "MobileAuth Example:",
          "content": "{\n    \"Authorization\":\"Digest eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBbmhEYWlEaWVuIjoiaHR0cDovL3N0b3JhZ2UuZ29vZ2xlYXBpcy5jb20vdGVzdF94bG95YWx0eTEvaW1hZ2VzL25oYWN1bmdjYXAvMWI5OWJkY2YtZDU4Mi00ZjQ5LTk3MTUtMWI2MWRmZmYzOTI0PzE0NzY3ODEyODY3MDciLCJOaGFDdW5nQ2FwSUQiOiIxYjk5YmRjZi1kNTgyLTRmNDktOTcxNS0xYjYxZGZmZjM5MjQiLCJUcmFuZ1RoYWlUaGFtR2lheFBvaW50IjozLCJNYU5oYUN1bmdDYXAiOiJQSU5HQ09NU0hPUCIsIlF1eWVuIjpbIlJPTEVfQ0FVSElOSERBTkhNVUNUSUNIRElFTU1VQUhBTkdPRkZMSU5FIiwiUk9MRV9DQVVISU5ITkhBTlRIT05HQkFPIiwiUk9MRV9DQVVISU5ITkhBTlRIT05HQkFPQ09OR05PIiwiUk9MRV9DQVVISU5IVEFOR0RJRU0iLCJST0xFX0NPTU1FTlQiLCJST0xFX0NVQUhBTkciLCJST0xFX0RBTkdOSEFQIiwiUk9MRV9EQVNIQk9BUkRfRElFTSIsIlJPTEVfREFTSEJPQVJEX0RPSVFVQSIsIlJPTEVfREFTSEJPQVJEX0ZPT1RUUkFGRklDIiwiUk9MRV9EQVNIQk9BUkRfUkFUSU5HQ1VBSEFORyIsIlJPTEVfREFTSEJPQVJEX1ZPVUNIRVIiLCJST0xFX0VYQ0VMVEFOR0RJRU0iLCJST0xFX0ZPT1RUUkFGRklDIiwiUk9MRV9LSEFDSEhBTkdEQU5HS1kiLCJST0xFX0tIVVlFTk1BSSIsIlJPTEVfTElDSFNVRE9JUVVBIiwiUk9MRV9MSUNIU1VLSEFDSEhBTkdEVU5HVk9VQ0hFUiIsIlJPTEVfTElDSFNVVEFOR0RJRU0iLCJST0xFX0xZRE9LSEFDSEhBTkdSQVRJTkdUSEFQIiwiUk9MRV9OSEFDVU5HQ0FQIiwiUk9MRV9RVUFOTFlDT05HTk9DSElUSUVUIiwiUk9MRV9RVUFOTFlUSEUiLCJST0xFX1FVQU5UUklCRUFDT04iLCJST0xFX1JBVElOR0NVQUhBTkciLCJST0xFX1JFUE9SVF9ET0lRVUEiLCJST0xFX1JFUE9SVF9QVVJDSEFTRUZPT1RUUkFGRklDIiwiUk9MRV9SRVBPUlRfVk9VQ0hFUiIsIlJPTEVfU0FOUEhBTSIsIlJPTEVfVEFPVEFJS0hPQU4iLCJST0xFX1RBT1RIRVRVRklMRSIsIlJPTEVfVk9VQ0hFUiJdLCJUZW5OaGFDdW5nQ2FwIjoiUElOR0NPTSBTSE9QIiwiaWF0IjoxNTAyMTU3NTAyLCJUZW5UcnV5Q2FwIjoicGluZ2NvbXNob3BAcGluZ2NvbXNob3AiLCJUYWlLaG9hblF1YW5UcmlJRCI6IjFhNzE0YTgzLWIxY2UtNDE4MS1hMDk4LTA0MjYyMDdlMWYzNCJ9.PmnQpD617Z9mkw5WubKemSJvYkZYiFz6E1NigL7IHYQ\"\n}",
          "type": "json"
        }
      ]
    },
    "error": {
      "fields": {
        "Error 4xx": [
          {
            "group": "Error 4xx",
            "optional": false,
            "field": "401-Unauthorized",
            "description": "\u003Cp\u003Etoken/api-key hết hạn hoặc sai định dạng. Yêu cầu login lại.\u003C/p\u003E \u003Cli\u003E\u003Ccode\u003Ecode:\u003C/code\u003E 401\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003Emessage:\u003C/code\u003E Mô tả lỗi.\u003C/li\u003E \u003Cbr/\u003E"
          },
          {
            "group": "Error 4xx",
            "optional": false,
            "field": "404-NotFound",
            "description": "\u003Cp\u003ELỗi khi truy cập dữ liệu không tồn tại.\u003C/p\u003E \u003Cli\u003E\u003Ccode\u003Ecode:\u003C/code\u003E Mã lỗi chi tiết. Ví dụ: 404, 100, 101, 102, 103,...\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003Emessage:\u003C/code\u003E Mô tả lỗi.\u003C/li\u003E \u003Cbr/\u003E"
          },
          {
            "group": "Error 4xx",
            "optional": false,
            "field": "405-NotAllowed",
            "description": "\u003Cp\u003ELỗi khi không có permission để thực hiện nghiệp vụ.\u003C/p\u003E \u003Cli\u003E\u003Ccode\u003Ecode:\u003C/code\u003E Mã lỗi chi tiết. Ví dụ: 405, 100, 101, 102, 103,...\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003Emessage:\u003C/code\u003E Mô tả lỗi.\u003C/li\u003E \u003Cbr/\u003E"
          },
          {
            "group": "Error 4xx",
            "optional": false,
            "field": "412-PreconditionFailed",
            "description": "\u003Cp\u003ELỗi kiểm tra điều kiện hợp lệ. Phát sinh trong quá trình check valid dữ liệu.\u003C/p\u003E \u003Cli\u003E\u003Ccode\u003Ecode:\u003C/code\u003E Mã lỗi chi tiết. Ví dụ: 412, 100, 101, 102, 103,...\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003Emessage:\u003C/code\u003E Mô tả lỗi.\u003C/li\u003E \u003Cbr/\u003E"
          }
        ],
        "Error 5xx": [
          {
            "group": "Error 5xx",
            "optional": false,
            "field": "500-InternalServerError",
            "description": "\u003Cp\u003ELỗi phát sinh khi nghiệp vụ xử lý lỗi.\u003C/p\u003E \u003Cli\u003E\u003Ccode\u003Ecode:\u003C/code\u003E500\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003Emessage:\u003C/code\u003E Mô tả lỗi.\u003C/li\u003E \u003Cbr/\u003E"
          }
        ]
      },
      "examples": [
        {
          "title": "HTTP/1.1 401",
          "content": "HTTP/1.1 401 Unauthorized\n{\n    \"code\": 401,\n    \"message\":\"Token is invalid or is expired. Please login again.\"\n}",
          "type": "json"
        },
        {
          "title": "HTTP/1.1 404",
          "content": "HTTP/1.1 404-NotFound\n{\n    \"code\": 100,\n    \"message\":\"Mô tả lỗi\"\n}",
          "type": "json"
        },
        {
          "title": "HTTP/1.1 405",
          "content": "HTTP/1.1 405-Not Allowed\n{\n    \"code\": 405,\n    \"message\":\"Bạn không được phép thực hiện chức năng này. Vui lòng liên hệ với quản lý của bạn.\"\n}",
          "type": "json"
        },
        {
          "title": "HTTP/1.1 412",
          "content": "HTTP/1.1 412-PreconditionFailed\n{\n    \"code\": 101,\n    \"message\":\"Mô tả lỗi\"\n}",
          "type": "json"
        },
        {
          "title": "HTTP/1.1 500",
          "content": "HTTP/1.1 500-InternalServerError\n{\n    \"code\": 500,\n    \"message\":\"Mô tả lỗi\"\n}",
          "type": "json"
        }
      ]
    }
  },
  {
    "type": "POST",
    "url": "{HOST}/api/v1.0/evaluation-cycles/mockup/actions/delete",
    "title": "Delete kỳ đánh giá",
    "description": "\u003Cp\u003EAPI xoá nhiều kỳ đánh giá\u003C/p\u003E",
    "group": "Mockup",
    "version": "1.0.0",
    "name": "DeleteEvaluationCycleMockupData",
    "parameter": {
      "fields": {
        "Body:": [
          {
            "group": "Body:",
            "type": "Array",
            "optional": false,
            "field": "evaluation_cycle_ids",
            "description": "\u003Cp\u003EDanh sách id kỳ đánh giá.\u003C/p\u003E"
          }
        ],
        "Query:": [
          {
            "group": "Query:",
            "type": "String",
            "allowedValues": [
              "vi",
              "en"
            ],
            "optional": true,
            "field": "lang",
            "defaultValue": "vi",
            "description": "\u003Cp\u003EMã ngôn ngữ. Nếu client không request lên mặc định là tiếng Việt.\u003C/p\u003E"
          }
        ]
      },
      "examples": [
        {
          "title": "Body example",
          "content": "{\n    \"evaluation_cycle_ids\": [\"1234567890\", \"1234567891\"]\n}",
          "type": "json"
        }
      ]
    },
    "success": {
      "fields": {
        "Success 200": [
          {
            "group": "Success 200",
            "type": "String",
            "optional": false,
            "field": "message",
            "description": "\u003Cp\u003EMô tả phản hồi.\u003C/p\u003E"
          },
          {
            "group": "Success 200",
            "type": "Integer",
            "optional": false,
            "field": "code",
            "description": "\u003Cp\u003EMã phản hồi.\u003C/p\u003E"
          }
        ]
      },
      "examples": [
        {
          "title": "Response: HTTP/1.1 200 OK",
          "content": "{\n    \"code\": \"200\",\n    \"message\": \"Delete success\"\n}",
          "type": "json"
        }
      ]
    },
    "filename": "src/ladder/mockup.py",
    "groupTitle": "Mockup",
    "header": {
      "fields": {
        "Headers:": [
          {
            "group": "Headers:",
            "type": "String",
            "optional": false,
            "field": "Authorization",
            "description": "\u003Cp\u003EToken/api-key để sử dụng api. Nếu typically là:\u003C/p\u003E \u003Cli\u003E\u003Ccode\u003EBearer\u003C/code\u003E thì Authenticate là AuthToken\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003EBasic\u003C/code\u003E thì Authenticate là API-Key\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003EDigest\u003C/code\u003E Sử dụng với Mobile API\u003C/li\u003E"
          }
        ]
      },
      "examples": [
        {
          "title": "AuthToken Example:",
          "content": "{\n    \"Authorization\":\"Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBbmhEYWlEaWVuIjoiaHR0cDovL3N0b3JhZ2UuZ29vZ2xlYXBpcy5jb20vdGVzdF94bG95YWx0eTEvaW1hZ2VzL25oYWN1bmdjYXAvMWI5OWJkY2YtZDU4Mi00ZjQ5LTk3MTUtMWI2MWRmZmYzOTI0PzE0NzY3ODEyODY3MDciLCJOaGFDdW5nQ2FwSUQiOiIxYjk5YmRjZi1kNTgyLTRmNDktOTcxNS0xYjYxZGZmZjM5MjQiLCJUcmFuZ1RoYWlUaGFtR2lheFBvaW50IjozLCJNYU5oYUN1bmdDYXAiOiJQSU5HQ09NU0hPUCIsIlF1eWVuIjpbIlJPTEVfQ0FVSElOSERBTkhNVUNUSUNIRElFTU1VQUhBTkdPRkZMSU5FIiwiUk9MRV9DQVVISU5ITkhBTlRIT05HQkFPIiwiUk9MRV9DQVVISU5ITkhBTlRIT05HQkFPQ09OR05PIiwiUk9MRV9DQVVISU5IVEFOR0RJRU0iLCJST0xFX0NPTU1FTlQiLCJST0xFX0NVQUhBTkciLCJST0xFX0RBTkdOSEFQIiwiUk9MRV9EQVNIQk9BUkRfRElFTSIsIlJPTEVfREFTSEJPQVJEX0RPSVFVQSIsIlJPTEVfREFTSEJPQVJEX0ZPT1RUUkFGRklDIiwiUk9MRV9EQVNIQk9BUkRfUkFUSU5HQ1VBSEFORyIsIlJPTEVfREFTSEJPQVJEX1ZPVUNIRVIiLCJST0xFX0VYQ0VMVEFOR0RJRU0iLCJST0xFX0ZPT1RUUkFGRklDIiwiUk9MRV9LSEFDSEhBTkdEQU5HS1kiLCJST0xFX0tIVVlFTk1BSSIsIlJPTEVfTElDSFNVRE9JUVVBIiwiUk9MRV9MSUNIU1VLSEFDSEhBTkdEVU5HVk9VQ0hFUiIsIlJPTEVfTElDSFNVVEFOR0RJRU0iLCJST0xFX0xZRE9LSEFDSEhBTkdSQVRJTkdUSEFQIiwiUk9MRV9OSEFDVU5HQ0FQIiwiUk9MRV9RVUFOTFlDT05HTk9DSElUSUVUIiwiUk9MRV9RVUFOTFlUSEUiLCJST0xFX1FVQU5UUklCRUFDT04iLCJST0xFX1JBVElOR0NVQUhBTkciLCJST0xFX1JFUE9SVF9ET0lRVUEiLCJST0xFX1JFUE9SVF9QVVJDSEFTRUZPT1RUUkFGRklDIiwiUk9MRV9SRVBPUlRfVk9VQ0hFUiIsIlJPTEVfU0FOUEhBTSIsIlJPTEVfVEFPVEFJS0hPQU4iLCJST0xFX1RBT1RIRVRVRklMRSIsIlJPTEVfVk9VQ0hFUiJdLCJUZW5OaGFDdW5nQ2FwIjoiUElOR0NPTSBTSE9QIiwiaWF0IjoxNTAyMTU3NTAyLCJUZW5UcnV5Q2FwIjoicGluZ2NvbXNob3BAcGluZ2NvbXNob3AiLCJUYWlLaG9hblF1YW5UcmlJRCI6IjFhNzE0YTgzLWIxY2UtNDE4MS1hMDk4LTA0MjYyMDdlMWYzNCJ9.PmnQpD617Z9mkw5WubKemSJvYkZYiFz6E1NigL7IHYQ\"\n}",
          "type": "json"
        },
        {
          "title": "Basic Token Example:",
          "content": "{\n    \"Authorization\":\"Basic 25630732-2976-444a-ba7a-3ca1b48fcc3a\"\n}",
          "type": "json"
        },
        {
          "title": "MobileAuth Example:",
          "content": "{\n    \"Authorization\":\"Digest eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBbmhEYWlEaWVuIjoiaHR0cDovL3N0b3JhZ2UuZ29vZ2xlYXBpcy5jb20vdGVzdF94bG95YWx0eTEvaW1hZ2VzL25oYWN1bmdjYXAvMWI5OWJkY2YtZDU4Mi00ZjQ5LTk3MTUtMWI2MWRmZmYzOTI0PzE0NzY3ODEyODY3MDciLCJOaGFDdW5nQ2FwSUQiOiIxYjk5YmRjZi1kNTgyLTRmNDktOTcxNS0xYjYxZGZmZjM5MjQiLCJUcmFuZ1RoYWlUaGFtR2lheFBvaW50IjozLCJNYU5oYUN1bmdDYXAiOiJQSU5HQ09NU0hPUCIsIlF1eWVuIjpbIlJPTEVfQ0FVSElOSERBTkhNVUNUSUNIRElFTU1VQUhBTkdPRkZMSU5FIiwiUk9MRV9DQVVISU5ITkhBTlRIT05HQkFPIiwiUk9MRV9DQVVISU5ITkhBTlRIT05HQkFPQ09OR05PIiwiUk9MRV9DQVVISU5IVEFOR0RJRU0iLCJST0xFX0NPTU1FTlQiLCJST0xFX0NVQUhBTkciLCJST0xFX0RBTkdOSEFQIiwiUk9MRV9EQVNIQk9BUkRfRElFTSIsIlJPTEVfREFTSEJPQVJEX0RPSVFVQSIsIlJPTEVfREFTSEJPQVJEX0ZPT1RUUkFGRklDIiwiUk9MRV9EQVNIQk9BUkRfUkFUSU5HQ1VBSEFORyIsIlJPTEVfREFTSEJPQVJEX1ZPVUNIRVIiLCJST0xFX0VYQ0VMVEFOR0RJRU0iLCJST0xFX0ZPT1RUUkFGRklDIiwiUk9MRV9LSEFDSEhBTkdEQU5HS1kiLCJST0xFX0tIVVlFTk1BSSIsIlJPTEVfTElDSFNVRE9JUVVBIiwiUk9MRV9MSUNIU1VLSEFDSEhBTkdEVU5HVk9VQ0hFUiIsIlJPTEVfTElDSFNVVEFOR0RJRU0iLCJST0xFX0xZRE9LSEFDSEhBTkdSQVRJTkdUSEFQIiwiUk9MRV9OSEFDVU5HQ0FQIiwiUk9MRV9RVUFOTFlDT05HTk9DSElUSUVUIiwiUk9MRV9RVUFOTFlUSEUiLCJST0xFX1FVQU5UUklCRUFDT04iLCJST0xFX1JBVElOR0NVQUhBTkciLCJST0xFX1JFUE9SVF9ET0lRVUEiLCJST0xFX1JFUE9SVF9QVVJDSEFTRUZPT1RUUkFGRklDIiwiUk9MRV9SRVBPUlRfVk9VQ0hFUiIsIlJPTEVfU0FOUEhBTSIsIlJPTEVfVEFPVEFJS0hPQU4iLCJST0xFX1RBT1RIRVRVRklMRSIsIlJPTEVfVk9VQ0hFUiJdLCJUZW5OaGFDdW5nQ2FwIjoiUElOR0NPTSBTSE9QIiwiaWF0IjoxNTAyMTU3NTAyLCJUZW5UcnV5Q2FwIjoicGluZ2NvbXNob3BAcGluZ2NvbXNob3AiLCJUYWlLaG9hblF1YW5UcmlJRCI6IjFhNzE0YTgzLWIxY2UtNDE4MS1hMDk4LTA0MjYyMDdlMWYzNCJ9.PmnQpD617Z9mkw5WubKemSJvYkZYiFz6E1NigL7IHYQ\"\n}",
          "type": "json"
        }
      ]
    },
    "error": {
      "fields": {
        "Error 4xx": [
          {
            "group": "Error 4xx",
            "optional": false,
            "field": "401-Unauthorized",
            "description": "\u003Cp\u003Etoken/api-key hết hạn hoặc sai định dạng. Yêu cầu login lại.\u003C/p\u003E \u003Cli\u003E\u003Ccode\u003Ecode:\u003C/code\u003E 401\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003Emessage:\u003C/code\u003E Mô tả lỗi.\u003C/li\u003E \u003Cbr/\u003E"
          },
          {
            "group": "Error 4xx",
            "optional": false,
            "field": "404-NotFound",
            "description": "\u003Cp\u003ELỗi khi truy cập dữ liệu không tồn tại.\u003C/p\u003E \u003Cli\u003E\u003Ccode\u003Ecode:\u003C/code\u003E Mã lỗi chi tiết. Ví dụ: 404, 100, 101, 102, 103,...\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003Emessage:\u003C/code\u003E Mô tả lỗi.\u003C/li\u003E \u003Cbr/\u003E"
          },
          {
            "group": "Error 4xx",
            "optional": false,
            "field": "405-NotAllowed",
            "description": "\u003Cp\u003ELỗi khi không có permission để thực hiện nghiệp vụ.\u003C/p\u003E \u003Cli\u003E\u003Ccode\u003Ecode:\u003C/code\u003E Mã lỗi chi tiết. Ví dụ: 405, 100, 101, 102, 103,...\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003Emessage:\u003C/code\u003E Mô tả lỗi.\u003C/li\u003E \u003Cbr/\u003E"
          },
          {
            "group": "Error 4xx",
            "optional": false,
            "field": "412-PreconditionFailed",
            "description": "\u003Cp\u003ELỗi kiểm tra điều kiện hợp lệ. Phát sinh trong quá trình check valid dữ liệu.\u003C/p\u003E \u003Cli\u003E\u003Ccode\u003Ecode:\u003C/code\u003E Mã lỗi chi tiết. Ví dụ: 412, 100, 101, 102, 103,...\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003Emessage:\u003C/code\u003E Mô tả lỗi.\u003C/li\u003E \u003Cbr/\u003E"
          }
        ],
        "Error 5xx": [
          {
            "group": "Error 5xx",
            "optional": false,
            "field": "500-InternalServerError",
            "description": "\u003Cp\u003ELỗi phát sinh khi nghiệp vụ xử lý lỗi.\u003C/p\u003E \u003Cli\u003E\u003Ccode\u003Ecode:\u003C/code\u003E500\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003Emessage:\u003C/code\u003E Mô tả lỗi.\u003C/li\u003E \u003Cbr/\u003E"
          }
        ]
      },
      "examples": [
        {
          "title": "HTTP/1.1 401",
          "content": "HTTP/1.1 401 Unauthorized\n{\n    \"code\": 401,\n    \"message\":\"Token is invalid or is expired. Please login again.\"\n}",
          "type": "json"
        },
        {
          "title": "HTTP/1.1 404",
          "content": "HTTP/1.1 404-NotFound\n{\n    \"code\": 100,\n    \"message\":\"Mô tả lỗi\"\n}",
          "type": "json"
        },
        {
          "title": "HTTP/1.1 405",
          "content": "HTTP/1.1 405-Not Allowed\n{\n    \"code\": 405,\n    \"message\":\"Bạn không được phép thực hiện chức năng này. Vui lòng liên hệ với quản lý của bạn.\"\n}",
          "type": "json"
        },
        {
          "title": "HTTP/1.1 412",
          "content": "HTTP/1.1 412-PreconditionFailed\n{\n    \"code\": 101,\n    \"message\":\"Mô tả lỗi\"\n}",
          "type": "json"
        },
        {
          "title": "HTTP/1.1 500",
          "content": "HTTP/1.1 500-InternalServerError\n{\n    \"code\": 500,\n    \"message\":\"Mô tả lỗi\"\n}",
          "type": "json"
        }
      ]
    }
  },
  {
    "type": "POST",
    "url": "{HOST}/api/v1.0/evaluations/mockup/actions/delete",
    "title": "Delete bản đánh giá",
    "description": "\u003Cp\u003EAPI xoá nhiều bản đánh giá\u003C/p\u003E",
    "group": "Mockup",
    "version": "1.0.0",
    "name": "DeleteEvaluationMockupData",
    "parameter": {
      "fields": {
        "Body:": [
          {
            "group": "Body:",
            "type": "Array",
            "optional": false,
            "field": "evaluation_cycle_ids",
            "description": "\u003Cp\u003EDanh sách \u003Ccode\u003Eid\u003C/code\u003E kỳ đánh giá muốn xoá bản đánh giá.\u003C/p\u003E"
          },
          {
            "group": "Body:",
            "type": "Array",
            "optional": true,
            "field": "employee_codes",
            "description": "\u003Cp\u003EDanh sách \u003Ccode\u003Emã nhân viên\u003C/code\u003E muốn xoá bản đánh giá. Nếu không truyền lên thì sẽ xoá hết bản đánh giá của kỳ này.\u003C/p\u003E"
          }
        ],
        "Query:": [
          {
            "group": "Query:",
            "type": "String",
            "allowedValues": [
              "vi",
              "en"
            ],
            "optional": true,
            "field": "lang",
            "defaultValue": "vi",
            "description": "\u003Cp\u003EMã ngôn ngữ. Nếu client không request lên mặc định là tiếng Việt.\u003C/p\u003E"
          }
        ]
      },
      "examples": [
        {
          "title": "Body example",
          "content": "{\n    \"evaluation_cycle_ids\": [\"1234567890\", \"1234567891\"],\n    \"employee_codes\": [\"1234567890\", \"1234567891\"]\n}",
          "type": "json"
        }
      ]
    },
    "success": {
      "fields": {
        "Success 200": [
          {
            "group": "Success 200",
            "type": "String",
            "optional": false,
            "field": "message",
            "description": "\u003Cp\u003EMô tả phản hồi.\u003C/p\u003E"
          },
          {
            "group": "Success 200",
            "type": "Integer",
            "optional": false,
            "field": "code",
            "description": "\u003Cp\u003EMã phản hồi.\u003C/p\u003E"
          }
        ]
      },
      "examples": [
        {
          "title": "Response: HTTP/1.1 200 OK",
          "content": "{\n    \"code\": \"200\",\n    \"message\": \"Delete success\"\n}",
          "type": "json"
        }
      ]
    },
    "filename": "src/ladder/mockup.py",
    "groupTitle": "Mockup",
    "header": {
      "fields": {
        "Headers:": [
          {
            "group": "Headers:",
            "type": "String",
            "optional": false,
            "field": "Authorization",
            "description": "\u003Cp\u003EToken/api-key để sử dụng api. Nếu typically là:\u003C/p\u003E \u003Cli\u003E\u003Ccode\u003EBearer\u003C/code\u003E thì Authenticate là AuthToken\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003EBasic\u003C/code\u003E thì Authenticate là API-Key\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003EDigest\u003C/code\u003E Sử dụng với Mobile API\u003C/li\u003E"
          }
        ]
      },
      "examples": [
        {
          "title": "AuthToken Example:",
          "content": "{\n    \"Authorization\":\"Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBbmhEYWlEaWVuIjoiaHR0cDovL3N0b3JhZ2UuZ29vZ2xlYXBpcy5jb20vdGVzdF94bG95YWx0eTEvaW1hZ2VzL25oYWN1bmdjYXAvMWI5OWJkY2YtZDU4Mi00ZjQ5LTk3MTUtMWI2MWRmZmYzOTI0PzE0NzY3ODEyODY3MDciLCJOaGFDdW5nQ2FwSUQiOiIxYjk5YmRjZi1kNTgyLTRmNDktOTcxNS0xYjYxZGZmZjM5MjQiLCJUcmFuZ1RoYWlUaGFtR2lheFBvaW50IjozLCJNYU5oYUN1bmdDYXAiOiJQSU5HQ09NU0hPUCIsIlF1eWVuIjpbIlJPTEVfQ0FVSElOSERBTkhNVUNUSUNIRElFTU1VQUhBTkdPRkZMSU5FIiwiUk9MRV9DQVVISU5ITkhBTlRIT05HQkFPIiwiUk9MRV9DQVVISU5ITkhBTlRIT05HQkFPQ09OR05PIiwiUk9MRV9DQVVISU5IVEFOR0RJRU0iLCJST0xFX0NPTU1FTlQiLCJST0xFX0NVQUhBTkciLCJST0xFX0RBTkdOSEFQIiwiUk9MRV9EQVNIQk9BUkRfRElFTSIsIlJPTEVfREFTSEJPQVJEX0RPSVFVQSIsIlJPTEVfREFTSEJPQVJEX0ZPT1RUUkFGRklDIiwiUk9MRV9EQVNIQk9BUkRfUkFUSU5HQ1VBSEFORyIsIlJPTEVfREFTSEJPQVJEX1ZPVUNIRVIiLCJST0xFX0VYQ0VMVEFOR0RJRU0iLCJST0xFX0ZPT1RUUkFGRklDIiwiUk9MRV9LSEFDSEhBTkdEQU5HS1kiLCJST0xFX0tIVVlFTk1BSSIsIlJPTEVfTElDSFNVRE9JUVVBIiwiUk9MRV9MSUNIU1VLSEFDSEhBTkdEVU5HVk9VQ0hFUiIsIlJPTEVfTElDSFNVVEFOR0RJRU0iLCJST0xFX0xZRE9LSEFDSEhBTkdSQVRJTkdUSEFQIiwiUk9MRV9OSEFDVU5HQ0FQIiwiUk9MRV9RVUFOTFlDT05HTk9DSElUSUVUIiwiUk9MRV9RVUFOTFlUSEUiLCJST0xFX1FVQU5UUklCRUFDT04iLCJST0xFX1JBVElOR0NVQUhBTkciLCJST0xFX1JFUE9SVF9ET0lRVUEiLCJST0xFX1JFUE9SVF9QVVJDSEFTRUZPT1RUUkFGRklDIiwiUk9MRV9SRVBPUlRfVk9VQ0hFUiIsIlJPTEVfU0FOUEhBTSIsIlJPTEVfVEFPVEFJS0hPQU4iLCJST0xFX1RBT1RIRVRVRklMRSIsIlJPTEVfVk9VQ0hFUiJdLCJUZW5OaGFDdW5nQ2FwIjoiUElOR0NPTSBTSE9QIiwiaWF0IjoxNTAyMTU3NTAyLCJUZW5UcnV5Q2FwIjoicGluZ2NvbXNob3BAcGluZ2NvbXNob3AiLCJUYWlLaG9hblF1YW5UcmlJRCI6IjFhNzE0YTgzLWIxY2UtNDE4MS1hMDk4LTA0MjYyMDdlMWYzNCJ9.PmnQpD617Z9mkw5WubKemSJvYkZYiFz6E1NigL7IHYQ\"\n}",
          "type": "json"
        },
        {
          "title": "Basic Token Example:",
          "content": "{\n    \"Authorization\":\"Basic 25630732-2976-444a-ba7a-3ca1b48fcc3a\"\n}",
          "type": "json"
        },
        {
          "title": "MobileAuth Example:",
          "content": "{\n    \"Authorization\":\"Digest eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBbmhEYWlEaWVuIjoiaHR0cDovL3N0b3JhZ2UuZ29vZ2xlYXBpcy5jb20vdGVzdF94bG95YWx0eTEvaW1hZ2VzL25oYWN1bmdjYXAvMWI5OWJkY2YtZDU4Mi00ZjQ5LTk3MTUtMWI2MWRmZmYzOTI0PzE0NzY3ODEyODY3MDciLCJOaGFDdW5nQ2FwSUQiOiIxYjk5YmRjZi1kNTgyLTRmNDktOTcxNS0xYjYxZGZmZjM5MjQiLCJUcmFuZ1RoYWlUaGFtR2lheFBvaW50IjozLCJNYU5oYUN1bmdDYXAiOiJQSU5HQ09NU0hPUCIsIlF1eWVuIjpbIlJPTEVfQ0FVSElOSERBTkhNVUNUSUNIRElFTU1VQUhBTkdPRkZMSU5FIiwiUk9MRV9DQVVISU5ITkhBTlRIT05HQkFPIiwiUk9MRV9DQVVISU5ITkhBTlRIT05HQkFPQ09OR05PIiwiUk9MRV9DQVVISU5IVEFOR0RJRU0iLCJST0xFX0NPTU1FTlQiLCJST0xFX0NVQUhBTkciLCJST0xFX0RBTkdOSEFQIiwiUk9MRV9EQVNIQk9BUkRfRElFTSIsIlJPTEVfREFTSEJPQVJEX0RPSVFVQSIsIlJPTEVfREFTSEJPQVJEX0ZPT1RUUkFGRklDIiwiUk9MRV9EQVNIQk9BUkRfUkFUSU5HQ1VBSEFORyIsIlJPTEVfREFTSEJPQVJEX1ZPVUNIRVIiLCJST0xFX0VYQ0VMVEFOR0RJRU0iLCJST0xFX0ZPT1RUUkFGRklDIiwiUk9MRV9LSEFDSEhBTkdEQU5HS1kiLCJST0xFX0tIVVlFTk1BSSIsIlJPTEVfTElDSFNVRE9JUVVBIiwiUk9MRV9MSUNIU1VLSEFDSEhBTkdEVU5HVk9VQ0hFUiIsIlJPTEVfTElDSFNVVEFOR0RJRU0iLCJST0xFX0xZRE9LSEFDSEhBTkdSQVRJTkdUSEFQIiwiUk9MRV9OSEFDVU5HQ0FQIiwiUk9MRV9RVUFOTFlDT05HTk9DSElUSUVUIiwiUk9MRV9RVUFOTFlUSEUiLCJST0xFX1FVQU5UUklCRUFDT04iLCJST0xFX1JBVElOR0NVQUhBTkciLCJST0xFX1JFUE9SVF9ET0lRVUEiLCJST0xFX1JFUE9SVF9QVVJDSEFTRUZPT1RUUkFGRklDIiwiUk9MRV9SRVBPUlRfVk9VQ0hFUiIsIlJPTEVfU0FOUEhBTSIsIlJPTEVfVEFPVEFJS0hPQU4iLCJST0xFX1RBT1RIRVRVRklMRSIsIlJPTEVfVk9VQ0hFUiJdLCJUZW5OaGFDdW5nQ2FwIjoiUElOR0NPTSBTSE9QIiwiaWF0IjoxNTAyMTU3NTAyLCJUZW5UcnV5Q2FwIjoicGluZ2NvbXNob3BAcGluZ2NvbXNob3AiLCJUYWlLaG9hblF1YW5UcmlJRCI6IjFhNzE0YTgzLWIxY2UtNDE4MS1hMDk4LTA0MjYyMDdlMWYzNCJ9.PmnQpD617Z9mkw5WubKemSJvYkZYiFz6E1NigL7IHYQ\"\n}",
          "type": "json"
        }
      ]
    },
    "error": {
      "fields": {
        "Error 4xx": [
          {
            "group": "Error 4xx",
            "optional": false,
            "field": "401-Unauthorized",
            "description": "\u003Cp\u003Etoken/api-key hết hạn hoặc sai định dạng. Yêu cầu login lại.\u003C/p\u003E \u003Cli\u003E\u003Ccode\u003Ecode:\u003C/code\u003E 401\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003Emessage:\u003C/code\u003E Mô tả lỗi.\u003C/li\u003E \u003Cbr/\u003E"
          },
          {
            "group": "Error 4xx",
            "optional": false,
            "field": "404-NotFound",
            "description": "\u003Cp\u003ELỗi khi truy cập dữ liệu không tồn tại.\u003C/p\u003E \u003Cli\u003E\u003Ccode\u003Ecode:\u003C/code\u003E Mã lỗi chi tiết. Ví dụ: 404, 100, 101, 102, 103,...\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003Emessage:\u003C/code\u003E Mô tả lỗi.\u003C/li\u003E \u003Cbr/\u003E"
          },
          {
            "group": "Error 4xx",
            "optional": false,
            "field": "405-NotAllowed",
            "description": "\u003Cp\u003ELỗi khi không có permission để thực hiện nghiệp vụ.\u003C/p\u003E \u003Cli\u003E\u003Ccode\u003Ecode:\u003C/code\u003E Mã lỗi chi tiết. Ví dụ: 405, 100, 101, 102, 103,...\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003Emessage:\u003C/code\u003E Mô tả lỗi.\u003C/li\u003E \u003Cbr/\u003E"
          },
          {
            "group": "Error 4xx",
            "optional": false,
            "field": "412-PreconditionFailed",
            "description": "\u003Cp\u003ELỗi kiểm tra điều kiện hợp lệ. Phát sinh trong quá trình check valid dữ liệu.\u003C/p\u003E \u003Cli\u003E\u003Ccode\u003Ecode:\u003C/code\u003E Mã lỗi chi tiết. Ví dụ: 412, 100, 101, 102, 103,...\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003Emessage:\u003C/code\u003E Mô tả lỗi.\u003C/li\u003E \u003Cbr/\u003E"
          }
        ],
        "Error 5xx": [
          {
            "group": "Error 5xx",
            "optional": false,
            "field": "500-InternalServerError",
            "description": "\u003Cp\u003ELỗi phát sinh khi nghiệp vụ xử lý lỗi.\u003C/p\u003E \u003Cli\u003E\u003Ccode\u003Ecode:\u003C/code\u003E500\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003Emessage:\u003C/code\u003E Mô tả lỗi.\u003C/li\u003E \u003Cbr/\u003E"
          }
        ]
      },
      "examples": [
        {
          "title": "HTTP/1.1 401",
          "content": "HTTP/1.1 401 Unauthorized\n{\n    \"code\": 401,\n    \"message\":\"Token is invalid or is expired. Please login again.\"\n}",
          "type": "json"
        },
        {
          "title": "HTTP/1.1 404",
          "content": "HTTP/1.1 404-NotFound\n{\n    \"code\": 100,\n    \"message\":\"Mô tả lỗi\"\n}",
          "type": "json"
        },
        {
          "title": "HTTP/1.1 405",
          "content": "HTTP/1.1 405-Not Allowed\n{\n    \"code\": 405,\n    \"message\":\"Bạn không được phép thực hiện chức năng này. Vui lòng liên hệ với quản lý của bạn.\"\n}",
          "type": "json"
        },
        {
          "title": "HTTP/1.1 412",
          "content": "HTTP/1.1 412-PreconditionFailed\n{\n    \"code\": 101,\n    \"message\":\"Mô tả lỗi\"\n}",
          "type": "json"
        },
        {
          "title": "HTTP/1.1 500",
          "content": "HTTP/1.1 500-InternalServerError\n{\n    \"code\": 500,\n    \"message\":\"Mô tả lỗi\"\n}",
          "type": "json"
        }
      ]
    }
  },
  {
    "type": "POST",
    "url": "{HOST}/api/v1.0/users/mockup/actions/delete",
    "title": "Delete user mockup data",
    "description": "\u003Cp\u003EAPI xoá nhiều nhân viên\u003C/p\u003E",
    "group": "Mockup",
    "version": "1.0.0",
    "name": "DeleteUserMockupData",
    "parameter": {
      "fields": {
        "Body:": [
          {
            "group": "Body:",
            "type": "Array",
            "optional": false,
            "field": "employee_codes",
            "description": "\u003Cp\u003EDanh sách mã nhân viên.\u003C/p\u003E"
          }
        ],
        "Query:": [
          {
            "group": "Query:",
            "type": "String",
            "allowedValues": [
              "vi",
              "en"
            ],
            "optional": true,
            "field": "lang",
            "defaultValue": "vi",
            "description": "\u003Cp\u003EMã ngôn ngữ. Nếu client không request lên mặc định là tiếng Việt.\u003C/p\u003E"
          }
        ]
      },
      "examples": [
        {
          "title": "Body example",
          "content": "{\n    \"employee_codes\": [\"1234567890\", \"1234567891\"]\n}",
          "type": "json"
        }
      ]
    },
    "success": {
      "fields": {
        "Success 200": [
          {
            "group": "Success 200",
            "type": "String",
            "optional": false,
            "field": "message",
            "description": "\u003Cp\u003EMô tả phản hồi.\u003C/p\u003E"
          },
          {
            "group": "Success 200",
            "type": "Integer",
            "optional": false,
            "field": "code",
            "description": "\u003Cp\u003EMã phản hồi.\u003C/p\u003E"
          }
        ]
      },
      "examples": [
        {
          "title": "Response: HTTP/1.1 200 OK",
          "content": "{\n    \"code\": \"200\",\n    \"message\": \"Delete success\"\n}",
          "type": "json"
        }
      ]
    },
    "filename": "src/ladder/mockup.py",
    "groupTitle": "Mockup",
    "header": {
      "fields": {
        "Headers:": [
          {
            "group": "Headers:",
            "type": "String",
            "optional": false,
            "field": "Authorization",
            "description": "\u003Cp\u003EToken/api-key để sử dụng api. Nếu typically là:\u003C/p\u003E \u003Cli\u003E\u003Ccode\u003EBearer\u003C/code\u003E thì Authenticate là AuthToken\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003EBasic\u003C/code\u003E thì Authenticate là API-Key\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003EDigest\u003C/code\u003E Sử dụng với Mobile API\u003C/li\u003E"
          }
        ]
      },
      "examples": [
        {
          "title": "AuthToken Example:",
          "content": "{\n    \"Authorization\":\"Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBbmhEYWlEaWVuIjoiaHR0cDovL3N0b3JhZ2UuZ29vZ2xlYXBpcy5jb20vdGVzdF94bG95YWx0eTEvaW1hZ2VzL25oYWN1bmdjYXAvMWI5OWJkY2YtZDU4Mi00ZjQ5LTk3MTUtMWI2MWRmZmYzOTI0PzE0NzY3ODEyODY3MDciLCJOaGFDdW5nQ2FwSUQiOiIxYjk5YmRjZi1kNTgyLTRmNDktOTcxNS0xYjYxZGZmZjM5MjQiLCJUcmFuZ1RoYWlUaGFtR2lheFBvaW50IjozLCJNYU5oYUN1bmdDYXAiOiJQSU5HQ09NU0hPUCIsIlF1eWVuIjpbIlJPTEVfQ0FVSElOSERBTkhNVUNUSUNIRElFTU1VQUhBTkdPRkZMSU5FIiwiUk9MRV9DQVVISU5ITkhBTlRIT05HQkFPIiwiUk9MRV9DQVVISU5ITkhBTlRIT05HQkFPQ09OR05PIiwiUk9MRV9DQVVISU5IVEFOR0RJRU0iLCJST0xFX0NPTU1FTlQiLCJST0xFX0NVQUhBTkciLCJST0xFX0RBTkdOSEFQIiwiUk9MRV9EQVNIQk9BUkRfRElFTSIsIlJPTEVfREFTSEJPQVJEX0RPSVFVQSIsIlJPTEVfREFTSEJPQVJEX0ZPT1RUUkFGRklDIiwiUk9MRV9EQVNIQk9BUkRfUkFUSU5HQ1VBSEFORyIsIlJPTEVfREFTSEJPQVJEX1ZPVUNIRVIiLCJST0xFX0VYQ0VMVEFOR0RJRU0iLCJST0xFX0ZPT1RUUkFGRklDIiwiUk9MRV9LSEFDSEhBTkdEQU5HS1kiLCJST0xFX0tIVVlFTk1BSSIsIlJPTEVfTElDSFNVRE9JUVVBIiwiUk9MRV9MSUNIU1VLSEFDSEhBTkdEVU5HVk9VQ0hFUiIsIlJPTEVfTElDSFNVVEFOR0RJRU0iLCJST0xFX0xZRE9LSEFDSEhBTkdSQVRJTkdUSEFQIiwiUk9MRV9OSEFDVU5HQ0FQIiwiUk9MRV9RVUFOTFlDT05HTk9DSElUSUVUIiwiUk9MRV9RVUFOTFlUSEUiLCJST0xFX1FVQU5UUklCRUFDT04iLCJST0xFX1JBVElOR0NVQUhBTkciLCJST0xFX1JFUE9SVF9ET0lRVUEiLCJST0xFX1JFUE9SVF9QVVJDSEFTRUZPT1RUUkFGRklDIiwiUk9MRV9SRVBPUlRfVk9VQ0hFUiIsIlJPTEVfU0FOUEhBTSIsIlJPTEVfVEFPVEFJS0hPQU4iLCJST0xFX1RBT1RIRVRVRklMRSIsIlJPTEVfVk9VQ0hFUiJdLCJUZW5OaGFDdW5nQ2FwIjoiUElOR0NPTSBTSE9QIiwiaWF0IjoxNTAyMTU3NTAyLCJUZW5UcnV5Q2FwIjoicGluZ2NvbXNob3BAcGluZ2NvbXNob3AiLCJUYWlLaG9hblF1YW5UcmlJRCI6IjFhNzE0YTgzLWIxY2UtNDE4MS1hMDk4LTA0MjYyMDdlMWYzNCJ9.PmnQpD617Z9mkw5WubKemSJvYkZYiFz6E1NigL7IHYQ\"\n}",
          "type": "json"
        },
        {
          "title": "Basic Token Example:",
          "content": "{\n    \"Authorization\":\"Basic 25630732-2976-444a-ba7a-3ca1b48fcc3a\"\n}",
          "type": "json"
        },
        {
          "title": "MobileAuth Example:",
          "content": "{\n    \"Authorization\":\"Digest eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBbmhEYWlEaWVuIjoiaHR0cDovL3N0b3JhZ2UuZ29vZ2xlYXBpcy5jb20vdGVzdF94bG95YWx0eTEvaW1hZ2VzL25oYWN1bmdjYXAvMWI5OWJkY2YtZDU4Mi00ZjQ5LTk3MTUtMWI2MWRmZmYzOTI0PzE0NzY3ODEyODY3MDciLCJOaGFDdW5nQ2FwSUQiOiIxYjk5YmRjZi1kNTgyLTRmNDktOTcxNS0xYjYxZGZmZjM5MjQiLCJUcmFuZ1RoYWlUaGFtR2lheFBvaW50IjozLCJNYU5oYUN1bmdDYXAiOiJQSU5HQ09NU0hPUCIsIlF1eWVuIjpbIlJPTEVfQ0FVSElOSERBTkhNVUNUSUNIRElFTU1VQUhBTkdPRkZMSU5FIiwiUk9MRV9DQVVISU5ITkhBTlRIT05HQkFPIiwiUk9MRV9DQVVISU5ITkhBTlRIT05HQkFPQ09OR05PIiwiUk9MRV9DQVVISU5IVEFOR0RJRU0iLCJST0xFX0NPTU1FTlQiLCJST0xFX0NVQUhBTkciLCJST0xFX0RBTkdOSEFQIiwiUk9MRV9EQVNIQk9BUkRfRElFTSIsIlJPTEVfREFTSEJPQVJEX0RPSVFVQSIsIlJPTEVfREFTSEJPQVJEX0ZPT1RUUkFGRklDIiwiUk9MRV9EQVNIQk9BUkRfUkFUSU5HQ1VBSEFORyIsIlJPTEVfREFTSEJPQVJEX1ZPVUNIRVIiLCJST0xFX0VYQ0VMVEFOR0RJRU0iLCJST0xFX0ZPT1RUUkFGRklDIiwiUk9MRV9LSEFDSEhBTkdEQU5HS1kiLCJST0xFX0tIVVlFTk1BSSIsIlJPTEVfTElDSFNVRE9JUVVBIiwiUk9MRV9MSUNIU1VLSEFDSEhBTkdEVU5HVk9VQ0hFUiIsIlJPTEVfTElDSFNVVEFOR0RJRU0iLCJST0xFX0xZRE9LSEFDSEhBTkdSQVRJTkdUSEFQIiwiUk9MRV9OSEFDVU5HQ0FQIiwiUk9MRV9RVUFOTFlDT05HTk9DSElUSUVUIiwiUk9MRV9RVUFOTFlUSEUiLCJST0xFX1FVQU5UUklCRUFDT04iLCJST0xFX1JBVElOR0NVQUhBTkciLCJST0xFX1JFUE9SVF9ET0lRVUEiLCJST0xFX1JFUE9SVF9QVVJDSEFTRUZPT1RUUkFGRklDIiwiUk9MRV9SRVBPUlRfVk9VQ0hFUiIsIlJPTEVfU0FOUEhBTSIsIlJPTEVfVEFPVEFJS0hPQU4iLCJST0xFX1RBT1RIRVRVRklMRSIsIlJPTEVfVk9VQ0hFUiJdLCJUZW5OaGFDdW5nQ2FwIjoiUElOR0NPTSBTSE9QIiwiaWF0IjoxNTAyMTU3NTAyLCJUZW5UcnV5Q2FwIjoicGluZ2NvbXNob3BAcGluZ2NvbXNob3AiLCJUYWlLaG9hblF1YW5UcmlJRCI6IjFhNzE0YTgzLWIxY2UtNDE4MS1hMDk4LTA0MjYyMDdlMWYzNCJ9.PmnQpD617Z9mkw5WubKemSJvYkZYiFz6E1NigL7IHYQ\"\n}",
          "type": "json"
        }
      ]
    },
    "error": {
      "fields": {
        "Error 4xx": [
          {
            "group": "Error 4xx",
            "optional": false,
            "field": "401-Unauthorized",
            "description": "\u003Cp\u003Etoken/api-key hết hạn hoặc sai định dạng. Yêu cầu login lại.\u003C/p\u003E \u003Cli\u003E\u003Ccode\u003Ecode:\u003C/code\u003E 401\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003Emessage:\u003C/code\u003E Mô tả lỗi.\u003C/li\u003E \u003Cbr/\u003E"
          },
          {
            "group": "Error 4xx",
            "optional": false,
            "field": "404-NotFound",
            "description": "\u003Cp\u003ELỗi khi truy cập dữ liệu không tồn tại.\u003C/p\u003E \u003Cli\u003E\u003Ccode\u003Ecode:\u003C/code\u003E Mã lỗi chi tiết. Ví dụ: 404, 100, 101, 102, 103,...\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003Emessage:\u003C/code\u003E Mô tả lỗi.\u003C/li\u003E \u003Cbr/\u003E"
          },
          {
            "group": "Error 4xx",
            "optional": false,
            "field": "405-NotAllowed",
            "description": "\u003Cp\u003ELỗi khi không có permission để thực hiện nghiệp vụ.\u003C/p\u003E \u003Cli\u003E\u003Ccode\u003Ecode:\u003C/code\u003E Mã lỗi chi tiết. Ví dụ: 405, 100, 101, 102, 103,...\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003Emessage:\u003C/code\u003E Mô tả lỗi.\u003C/li\u003E \u003Cbr/\u003E"
          },
          {
            "group": "Error 4xx",
            "optional": false,
            "field": "412-PreconditionFailed",
            "description": "\u003Cp\u003ELỗi kiểm tra điều kiện hợp lệ. Phát sinh trong quá trình check valid dữ liệu.\u003C/p\u003E \u003Cli\u003E\u003Ccode\u003Ecode:\u003C/code\u003E Mã lỗi chi tiết. Ví dụ: 412, 100, 101, 102, 103,...\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003Emessage:\u003C/code\u003E Mô tả lỗi.\u003C/li\u003E \u003Cbr/\u003E"
          }
        ],
        "Error 5xx": [
          {
            "group": "Error 5xx",
            "optional": false,
            "field": "500-InternalServerError",
            "description": "\u003Cp\u003ELỗi phát sinh khi nghiệp vụ xử lý lỗi.\u003C/p\u003E \u003Cli\u003E\u003Ccode\u003Ecode:\u003C/code\u003E500\u003C/li\u003E \u003Cli\u003E\u003Ccode\u003Emessage:\u003C/code\u003E Mô tả lỗi.\u003C/li\u003E \u003Cbr/\u003E"
          }
        ]
      },
      "examples": [
        {
          "title": "HTTP/1.1 401",
          "content": "HTTP/1.1 401 Unauthorized\n{\n    \"code\": 401,\n    \"message\":\"Token is invalid or is expired. Please login again.\"\n}",
          "type": "json"
        },
        {
          "title": "HTTP/1.1 404",
          "content": "HTTP/1.1 404-NotFound\n{\n    \"code\": 100,\n    \"message\":\"Mô tả lỗi\"\n}",
          "type": "json"
        },
        {
          "title": "HTTP/1.1 405",
          "content": "HTTP/1.1 405-Not Allowed\n{\n    \"code\": 405,\n    \"message\":\"Bạn không được phép thực hiện chức năng này. Vui lòng liên hệ với quản lý của bạn.\"\n}",
          "type": "json"
        },
        {
          "title": "HTTP/1.1 412",
          "content": "HTTP/1.1 412-PreconditionFailed\n{\n    \"code\": 101,\n    \"message\":\"Mô tả lỗi\"\n}",
          "type": "json"
        },
        {
          "title": "HTTP/1.1 500",
          "content": "HTTP/1.1 500-InternalServerError\n{\n    \"code\": 500,\n    \"message\":\"Mô tả lỗi\"\n}",
          "type": "json"
        }
      ]
    }
  }