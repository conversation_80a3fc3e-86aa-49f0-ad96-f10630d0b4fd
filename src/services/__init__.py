#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 17/08/2025
"""


from mobio.libs.logging import MobioLogging
from starlette.requests import Request
from src.auth.encrypt import decode_oauth2_token
from src.common.common import UserKey


class BaseService:
    def __init__(self):
        self.logging = MobioLogging()

    async def get_user_id_from_token(self, request: Request):
        authorization = request.headers.get("Authorization")
        if authorization.startswith("Bearer "):
            authorization = authorization.replace("Bearer ", "")

        payload_decode: dict = await decode_oauth2_token(authorization)
        return payload_decode.get(UserKey.USER_ID)

    async def get_roles_and_permissions_from_token(self, request: Request):
        authorization = request.headers.get("Authorization")
        if authorization.startswith("Bearer "):
            authorization = authorization.replace("Bearer ", "")

        payload_decode: dict = await decode_oauth2_token(authorization)
        roles = payload_decode.get(UserKey.ROLES)
        permissions = payload_decode.get(UserKey.PERMISSIONS)
        return roles, permissions
