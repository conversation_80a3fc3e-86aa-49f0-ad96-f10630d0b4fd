#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: LocDX
    Company: MobioVN
    Date created: 20/08/2025
"""
from src.common.choices import QueryParamChoice, RoleChoice
from src.repositories.v1.departments_repository import DepartmentsRepository
from src.repositories.v1.users_repository import UsersRepository
from src.schemas.pydantic.department_schemas import GetDepartmentsSchema
from src.services import BaseService


class DepartmentsService(BaseService):
    def __init__(self):
        super().__init__()
        self.departments_repository = DepartmentsRepository()
        self.users_repository = UsersRepository()

    async def get_department_ids_for_abac(self, roles, user):

        # [!] Nếu là admin thì trả về tất cả các phòng ban
        department_ids = []
        if RoleChoice.ADMIN.value in roles:
            return []

        if RoleChoice.LEADER.value in roles or RoleChoice.USER.value in roles:
            department_ids.extend([department.department_id for department in user.departments])

        return department_ids

    async def get_departments(
            self,
            request,
            session,
            search,
            sort,
            order,
            page,
            per_page,
            is_show_department_hidden,
            company_id,
            account_id
    ):
        display = None
        if is_show_department_hidden == 1:
            display = 0
        elif is_show_department_hidden == 0:
            display = 1

        request_department_ids = request.query_params.get(QueryParamChoice.DEPARTMENT_IDS)
        if request_department_ids:
            request_department_ids = request_department_ids.split(",")

        roles, _ = await self.get_roles_and_permissions_from_token(request)
        user = await self.users_repository.get_user_by_id(account_id)

        department_ids = await self.get_department_ids_for_abac(roles, user)

        list_department_id_query = []
        if request_department_ids:
            if not department_ids:
                list_department_id_query = request_department_ids
            else:
                for request_department_id in request_department_ids:
                    if request_department_id in department_ids:
                        list_department_id_query.append(request_department_id)
        else:
            list_department_id_query = department_ids

        departments, paging = await self.departments_repository.get_departments(
            search, list_department_id_query, company_id, sort, order, page, per_page, display
        )

        results = []
        for department in departments:
            get_department_schema = GetDepartmentsSchema(
                department_id=department.get('department_id'),
                name=department.get('name'),
                display=department.get('display'),
                level=1,
                order=department.get('order'),
            )
            results.append(get_department_schema.dict(exclude_none=True))

        return results, paging

    async def get_departments_for_org_chart(
            self,
            search,
            sort,
            order,
            page,
            per_page,
            department_ids,
            company_id,
            display,
    ):
        departments, paging = await self.departments_repository.get_departments(
            search, department_ids, company_id, sort, order, page, per_page, display
        )
        results = []
        for department in departments:
            get_department_schema = GetDepartmentsSchema(
                department_id=department.get('department_id'),
                name=department.get('name'),
                display=department.get('display'),
                level=1,
                order=department.get('order'),
            )
            results.append(get_department_schema.dict(exclude_none=True))
        return results, paging
