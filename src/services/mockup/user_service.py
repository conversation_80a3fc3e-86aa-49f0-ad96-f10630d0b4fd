#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 19/08/2025
"""

from src.common.init_common.logging import log_project
from src.repositories.mongodb.mockup.employment_type_repository import (
    EmploymentTypeRepository,
)
from src.repositories.mongodb.mockup.user_repository import UserRepository
from src.schemas.pydantic.mockup.user_schemas import (
    MockupAddUserRequestSchema,
    UserMockupDataSchema,
)
from src.services import BaseService


class UserMockupService(BaseService):
    def __init__(self):
        super().__init__()
        self.user_repository = UserRepository()
        self.employment_type_repository = EmploymentTypeRepository()

    async def add_users(self, payload: MockupAddUserRequestSchema) -> tuple[bool, str]:
        log_project.info(f"start function {self.add_users.__name__} with payload: {payload}")

        user_data = payload.data

        mapping_employee = await self._mapping_employee_name_and_id()

        mapping_employee_code_with_user_id = await self._get_users_in_db_to_mapping_employee_code()

        for user in user_data:
            log_project.info(f"start prepare data for add users: {user}")
            user_employee_code = user.employee_code

        return True, "Add users success"

    async def _get_users_in_db_to_mapping_employee_code(self) -> dict:
        user_in_database = await self.user_repository.find_all_user_not_paging()

        mapping_employee_code_with_user_id = {}
        for user in user_in_database:
            mapping_employee_code_with_user_id[user["employee_code"]] = user["user_id"]
        return mapping_employee_code_with_user_id

    async def _mapping_employee_name_and_id(self) -> dict:
        mapping_employee_name_and_id = {}
        employment_types = await self.employment_type_repository.find_all_not_paging()
        for employment_type in employment_types:
            employee_type_name = employment_type["vi_name"]
            employee_id_in_db = employment_type["employment_type_id"]

            key_employee_name = employee_type_name.strip().lower()

            mapping_employee_name_and_id[key_employee_name] = {
                "type_name": employee_type_name,
                "type_id": employee_id_in_db,
            }
        return mapping_employee_name_and_id

    async def _build_data_mockup_user_add(self, payload: UserMockupDataSchema) -> dict:
        employee_type = await self._get_employee_type_id()
        return {"employee_type": employee_type}
    
    async def _get_job_title_of_department(self) -> dict:
        pass
    
    async def get_param_common_by_data(self, payload: MockupAddUserRequestSchema) -> dict:
        
        data = payload.data
        for item in data:
            pass
            
        
