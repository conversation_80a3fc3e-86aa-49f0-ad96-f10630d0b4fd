#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: LocDX
    Company: MobioVN
    Date created: 20/08/2025
"""
from src.common.choices import StatusChoice
from src.common.common import CommonKey
from src.models.mongo import MongoBaseModel
from src.models.mongodb.departments_model import DepartmentsModel
from src.utils import utf8_to_ascii


class DepartmentsRepository():

    def __init__(self):
        super().__init__()
        self.departments_model = DepartmentsModel()

    async def count_departments(self):
        return await self.departments_model.count({"status": StatusChoice.ACTIVATE.value})

    async def get_departments(
        self, search="", department_ids=[], company_id="", sort="updated_time", order=-1, page=1, per_page=20, display=1
    ):
        query = {"company_id": company_id, "status": StatusChoice.ACTIVATE.value}
        if display is not None:
            query.update({"display": display})
        if search:
            search = utf8_to_ascii(search.lower())
            query.update({"lower_case_name": {"$regex": search}})
        if department_ids:
            query.update({"department_id": {"$in": department_ids}})
        results = await self.departments_model.find_paginate(
            query,
            page=page,
            per_page=per_page,
            sort=sort,
            order=order,
        )
        total_count = await self.departments_model.count(query)
        return list(results), {
            CommonKey.PAGE: page,
            CommonKey.PER_PAGE: per_page,
            CommonKey.TOTAL_COUNT: total_count,
            CommonKey.TOTAL_PAGE: total_count // per_page + 1,
        }
