#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: LocDX
    Company: MobioVN
    Date created: 20/08/2025
"""
from src.common.choices import StatusChoice
from src.models.mongodb.users_model import UsersModel


class UsersRepository():

    def __init__(self):
        super().__init__()
        self.users_model = UsersModel()

    async def count_users(self):
        return await self.users_model.count({"status": StatusChoice.ACTIVATE.value})

    async def get_user_by_id(self, user_id):
        return await self.users_model.find_one({"user_id": user_id})
