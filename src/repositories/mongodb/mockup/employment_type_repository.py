#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 19/08/2025
"""

from src.models.postgres.base_model import EmploymentTypeModel


class EmploymentTypeRepository:
    def __init__(self):
        self.employment_type_model = EmploymentTypeModel()

    async def find_all_not_paging(self):
        employment_types = await self.employment_type_model.find()
        return employment_types
