#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 19/08/2025
"""


from src.models.mongodb.user_model import UserModel


class UserRepository:
    def __init__(self):
        self.user_model = UserModel()

    async def find_all_user_not_paging(self):
        """
        Find all users without pagination
        Returns list of user documents
        """
        users = await self.user_model.find()
        return users
