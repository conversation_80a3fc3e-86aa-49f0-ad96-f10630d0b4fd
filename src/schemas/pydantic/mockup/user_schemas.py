#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 19/08/2025
"""

from typing import List, Optional
from pydantic import BaseModel, Field


class UserMockupDataSchema(BaseModel):
    employee_code: str = Field(..., description="Mã nhân viên")
    name: str = Field(..., description="Tên nhân viên")
    email_work: str = Field(..., description="Email làm việc của nhân viên")
    personal_email: Optional[str] = Field(None, description="Email cá nhân của nhân viên")
    gender: str = Field(..., description="Giới tính: 1=Nam, 2=Nữ")
    date_of_birth: str = Field(..., description="Ngày sinh Format: YYYY-MM-DD")
    phone_number: str = Field(..., description="Số điện thoại của nhân viên")
    leader_email: Optional[str] = Field(None, description="Email leader của nhân viên")
    department_name: str = Field(..., description="Tên phòng ban của nhân viên")
    job_title_name: str = Field(..., description="Tên chức danh của nhân viên")
    employee_type: str = Field(..., description="Loại nhân viên: 1=Chính thức, 2=Thử việc")
    start_onboard_at: str = Field(..., description="Ngày làm việc Format: YYYY-MM-DD")
    avatar_link: str = Field(..., description="Link avatar của nhân viên")
    thumb_avatar_link: str = Field(..., description="Link avatar nhỏ của nhân viên")
    is_leader: bool = Field(..., description="Có phải leader hay không")


class MockupAddUserRequestSchema(BaseModel):
    data: List[UserMockupDataSchema] = Field(..., description="Danh sách nhân viên, tối đa 200 phần tử", max_items=200)
