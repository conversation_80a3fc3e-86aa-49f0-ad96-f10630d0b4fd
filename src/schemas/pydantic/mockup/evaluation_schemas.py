#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 19/08/2025
"""

from typing import List, Optional
from pydantic import BaseModel, Field


class MockupDeleteEvaluationCycleRequestSchema(BaseModel):
    evaluation_cycle_ids: List[str] = Field(..., description="Danh sách id kỳ đánh giá")


class MockupDeleteEvaluationRequestSchema(BaseModel):
    evaluation_cycle_ids: List[str] = Field(..., description="Danh sách id kỳ đánh giá muốn xoá bản đánh giá")
    employee_codes: Optional[List[str]] = Field(None, description="Danh sách mã nhân viên muốn xoá bản đánh giá")


class MockupDeleteUserRequestSchema(BaseModel):
    employee_codes: List[str] = Field(..., description="Danh sách mã nhân viên")