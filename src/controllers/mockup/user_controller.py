#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 19/08/2025
"""

from fastapi import Request

from src.common.custom_exception import CustomError
from src.common.init_common.logging import log_project
from src.controllers.base_controller import BaseController
from src.schemas.pydantic.mockup.user_schemas import MockupAddUserRequestSchema
from src.services.mockup.user_service import UserMockupService


class UserController(BaseController):
    def __init__(self):
        super().__init__()
        self.user_service = UserMockupService()

    async def add_users(self, request: Request, payload: MockupAddUserRequestSchema):
        log_project.info(f"start function {self.add_users.__name__} with payload: {payload}")
        is_success, message = await self.user_service.add_users(payload)
        if not is_success:
            raise CustomError(message)

        return {
            "message": message,
        }
