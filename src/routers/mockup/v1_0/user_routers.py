#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 19/08/2025
"""
from fastapi import APIRouter, Request

from src.controllers.mockup.user_controller import UserController
from src.middleware.handle_response import CustomHandleResponseRoute
from src.schemas.pydantic import BaseResponseSchema
from src.schemas.pydantic.mockup.user_schemas import MockupAddUserRequestSchema

routers = APIRouter(route_class=CustomHandleResponseRoute)


@routers.post(
    path="/users/mockup/actions/add",
    dependencies=[],
    response_model=BaseResponseSchema,
)
async def add_users(request: Request, payload: MockupAddUserRequestSchema):
    return await UserController().add_users(request, payload)
